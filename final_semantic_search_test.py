from sentence_transformers import SentenceTransformer
from supabase import create_client, Client

# Configuration
SUPABASE_URL = "https://emohcpmxwwqhlbzfqklm.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtb2hjcG14d3dxaGxiemZxa2xtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNTAxNSwiZXhwIjoyMDY2NjExMDE1fQ.Gz4oKMdcB5_90huXT4r6fJWSIGgnwi4shYIgG7KkPec"
EMBEDDING_MODEL = "all-MiniLM-L6-v2"

def semantic_search(query, limit=5):
    """Perform semantic search on document chunks."""
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    model = SentenceTransformer(EMBEDDING_MODEL)
    
    print(f"🔍 Vyhledávám: '{query}'")
    
    # Generate embedding for the query
    query_embedding = model.encode(query).tolist()
    
    # Perform vector similarity search
    result = supabase.rpc(
        'match_documents',
        {
            'query_embedding': query_embedding,
            'match_threshold': 0.0,  # No threshold to get all results
            'match_count': limit
        }
    ).execute()
    
    print(f"📊 Nalezeno {len(result.data)} výsledků:\n")
    
    for i, item in enumerate(result.data, 1):
        similarity = item['similarity']
        pages = item['page_numbers']
        page_info = f"str. {min(pages)}-{max(pages)}" if len(pages) > 1 else f"str. {pages[0]}" if pages else "N/A"
        
        print(f"{i}. 🎯 Podobnost: {similarity:.3f} | {page_info}")
        print(f"   📄 Text: {item['chunk_text'][:300]}{'...' if len(item['chunk_text']) > 300 else ''}")
        print(f"   📋 Metadata: {item['metadata'].get('document_title', 'N/A')}")
        print()

def main():
    """Test various search queries."""
    test_queries = [
        "manželství",
        "vlastnictví nemovitosti", 
        "smlouva o dílo",
        "náhrada škody",
        "dědictví"
    ]
    
    print("🚀 Test sémantického vyhledávání v Občanském zákoníku\n")
    print("=" * 60)
    
    for query in test_queries:
        try:
            semantic_search(query, limit=3)
            print("-" * 60)
        except Exception as e:
            print(f"❌ Chyba při vyhledávání '{query}': {e}")
            print("-" * 60)

if __name__ == "__main__":
    main()
