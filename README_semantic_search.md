# Sémantické vyhledávání v Občanském zákoníku

Tento projekt implementuje sémantické vyhledávání nad dokumentem Občanského zákoníku (89/2012 Sb.) pomocí Supabase databáze a vektorových embeddingů.

## 🏗️ Architektura

### Komponenty systému:
1. **PDF extrakce a chunking** - `import_pdf_to_czechlaws3.py`
2. **Supabase databáze** - PostgreSQL s pgvector extension
3. **Embedding model** - `all-MiniLM-L6-v2` (384 dimenzí)
4. **Vyhledávací rozhraní** - Python skripty pro interaktivní vyhledávání

### Databázová struktura:

```sql
CREATE TABLE document_chunks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_name VARCHAR(255) NOT NULL,
  document_type VARCHAR(50) DEFAULT 'pdf',
  chunk_text TEXT NOT NULL,
  chunk_index INTEGER NOT NULL,
  page_numbers INTEGER[] NOT NULL,
  start_page INTEGER NOT NULL,
  end_page INTEGER NOT NULL,
  embedding vector(384),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Indexy pro optimalizaci:
- `idx_document_chunks_document_name` - pro filtrování podle dokumentu
- `idx_document_chunks_page_numbers` - pro vyhledávání podle stránek
- `idx_document_chunks_embedding` - IVFFlat index pro vektorové vyhledávání
- `idx_document_chunks_metadata` - GIN index pro metadata

## 📊 Data

### Statistiky dokumentu:
- **Dokument**: Občanský zákoník (89/2012 Sb.)
- **Datum účinnosti**: 2025-06-01
- **Celkem chunků**: 1,250
- **Rozsah stránek**: 1-594
- **Velikost chunku**: ~1000 znaků

### Metadata pro každý chunk:
```json
{
  "source_file": "Sb_2012_89_2025-06-01_IZ.chunks.json",
  "document_title": "Občanský zákoník",
  "document_number": "89/2012 Sb.",
  "effective_date": "2025-06-01",
  "chunk_length": 1024,
  "page_span": 2
}
```

## 🚀 Použití

### 1. Import dat do Supabase:
```bash
python import_chunks_to_supabase.py
```

### 2. Interaktivní vyhledávání:
```bash
python interactive_search.py
```

### 3. Test vyhledávání:
```bash
python final_semantic_search_test.py
```

## 🔍 Vyhledávací funkce

### PostgreSQL funkce `match_documents`:
```sql
SELECT * FROM match_documents(
    query_embedding,  -- vector(384)
    match_threshold,  -- float (0.0-1.0)
    match_count      -- int
);
```

### Příklady dotazů:
- `"manželství"` - najde paragrafy o manželství
- `"vlastnictví nemovitosti"` - najde ustanovení o vlastnických právech
- `"smlouva o dílo"` - najde smluvní právo
- `"náhrada škody"` - najde ustanovení o odpovědnosti

## 📈 Výkon

### Podobnost (cosine similarity):
- **0.3+**: Velmi relevantní výsledky
- **0.2-0.3**: Středně relevantní výsledky  
- **0.1-0.2**: Slabě relevantní výsledky
- **<0.1**: Nerelevantní výsledky

### Optimalizace:
- IVFFlat index pro rychlé vektorové vyhledávání
- Batch inserting při importu dat
- Filtrování podle minimální podobnosti

## 🛠️ Technické požadavky

### Python balíčky:
```
sentence-transformers==4.1.0
supabase>=2.16.0
PyPDF2
```

### Supabase konfigurace:
- PostgreSQL 15+
- pgvector extension
- Service role key pro API přístup

## 📝 Soubory

### Hlavní skripty:
- `import_chunks_to_supabase.py` - Import chunků do Supabase
- `interactive_search.py` - Interaktivní vyhledávání
- `final_semantic_search_test.py` - Test různých dotazů

### Pomocné skripty:
- `test_search_direct.py` - Přímý test vyhledávání
- `test_simple_search.py` - Jednoduchý test databáze
- `check_embedding_size.py` - Kontrola velikosti embeddingů

### Data:
- `Sb_2012_89_2025-06-01_IZ.pdf` - Původní PDF dokument
- `Sb_2012_89_2025-06-01_IZ.chunks.json` - Extrahované chunky

## 🎯 Výsledky

Systém úspěšně implementuje sémantické vyhledávání s těmito vlastnostmi:

✅ **Funkční vektorové vyhledávání** - Cosine similarity search
✅ **Rychlé dotazy** - Optimalizované indexy
✅ **Relevantní výsledky** - Kvalitní embeddingy
✅ **Metadata** - Informace o stránkách a kontextu
✅ **Škálovatelnost** - Připraveno pro více dokumentů

### Příklad výsledku:
```
🔍 Vyhledávám: 'manželství'
📊 Nalezeno 3 výsledků:

1. 🎯 Podobnost: 0.368 | str. 125-126
   📄 Text: O d d í l  2 N e p l a t n o s t  m a n ž e l s t v í
   § 680 Došlo-li k uzavření manželství, přestože tomu bránila 
   zákonná překážka, soud prohlásí manželství za neplatné...
```

## 🔮 Možná rozšíření

1. **Více dokumentů** - Import dalších zákonů
2. **Hybridní vyhledávání** - Kombinace sémantického a fulltextového
3. **API endpoint** - REST API pro externí aplikace
4. **Web interface** - Webová aplikace pro vyhledávání
5. **Citace** - Automatické generování citací paragrafů
