from sentence_transformers import SentenceTransformer
from supabase import create_client, Client

# Configuration
SUPABASE_URL = "https://emohcpmxwwqhlbzfqklm.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtb2hjcG14d3dxaGxiemZxa2xtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNTAxNSwiZXhwIjoyMDY2NjExMDE1fQ.Gz4oKMdcB5_90huXT4r6fJWSIGgnwi4shYIgG7KkPec"
EMBEDDING_MODEL = "all-MiniLM-L6-v2"

def test_search():
    # Initialize clients
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    model = SentenceTransformer(EMBEDDING_MODEL)
    
    # Test query
    query = "manželství"
    print(f"Testovací dotaz: '{query}'")
    
    # Generate embedding
    query_embedding = model.encode(query).tolist()
    print(f"Embedding vygenerován, délka: {len(query_embedding)}")
    
    # Test the search function
    try:
        result = supabase.rpc(
            'match_documents',
            {
                'query_embedding': query_embedding,
                'match_threshold': 0.5,
                'match_count': 3
            }
        ).execute()
        
        print(f"Nalezeno {len(result.data)} výsledků:")
        for i, item in enumerate(result.data, 1):
            print(f"\n{i}. Podobnost: {item['similarity']:.3f}")
            print(f"   Stránky: {item['start_page']}-{item['end_page']}")
            print(f"   Text: {item['chunk_text'][:200]}...")
            
    except Exception as e:
        print(f"Chyba při vyhledávání: {e}")

if __name__ == "__main__":
    test_search()
