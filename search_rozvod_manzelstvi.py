from sentence_transformers import SentenceTransformer
from supabase import create_client, Client

# Configuration
SUPABASE_URL = "https://emohcpmxwwqhlbzfqklm.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtb2hjcG14d3dxaGxiemZxa2xtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNTAxNSwiZXhwIjoyMDY2NjExMDE1fQ.Gz4oKMdcB5_90huXT4r6fJWSIGgnwi4shYIgG7KkPec"
EMBEDDING_MODEL = "all-MiniLM-L6-v2"

def search_divorce_topics():
    """Search for information about divorce in the Civil Code."""
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    model = SentenceTransformer(EMBEDDING_MODEL)
    
    # Different search queries related to divorce
    queries = [
        "rozvod manželství",
        "rozvodové řízení", 
        "důvody rozvodu",
        "roz<PERSON><PERSON><PERSON> manželů",
        "zánik manželství",
        "rozvod soudem",
        "spole<PERSON>n<PERSON> jměn<PERSON> man<PERSON>el<PERSON> rozvod",
        "péče o děti po rozvodu",
        "výživné po rozvodu"
    ]
    
    all_results = []
    seen_chunks = set()  # To avoid duplicates
    
    print("🔍 Vyhledávám informace o rozvodu manželství...\n")
    
    for query in queries:
        print(f"Dotaz: '{query}'")
        
        # Generate embedding
        query_embedding = model.encode(query).tolist()
        
        # Search
        result = supabase.rpc(
            'match_documents',
            {
                'query_embedding': query_embedding,
                'match_threshold': 0.15,  # Lower threshold for broader search
                'match_count': 10
            }
        ).execute()
        
        # Add unique results
        for item in result.data:
            chunk_id = item['id']
            if chunk_id not in seen_chunks and item['similarity'] >= 0.15:
                seen_chunks.add(chunk_id)
                all_results.append({
                    'query': query,
                    'similarity': item['similarity'],
                    'text': item['chunk_text'],
                    'pages': item['page_numbers'],
                    'start_page': item['start_page'],
                    'end_page': item['end_page']
                })
        
        print(f"  → {len(result.data)} výsledků")
    
    # Sort by similarity
    all_results.sort(key=lambda x: x['similarity'], reverse=True)
    
    print(f"\n📊 Celkem nalezeno {len(all_results)} unikátních chunků o rozvodu\n")
    print("="*80)
    
    return all_results

def generate_summary(results):
    """Generate a summary about divorce based on search results."""
    
    print("📋 SUMMARY: ROZVOD MANŽELSTVÍ V OBČANSKÉM ZÁKONÍKU")
    print("="*80)
    
    # Group results by relevance
    high_relevance = [r for r in results if r['similarity'] >= 0.3]
    medium_relevance = [r for r in results if 0.2 <= r['similarity'] < 0.3]
    
    print(f"\n🎯 NEJVÍCE RELEVANTNÍ USTANOVENÍ ({len(high_relevance)} chunků):")
    print("-" * 60)
    
    for i, result in enumerate(high_relevance[:10], 1):
        pages = result['pages']
        page_info = f"str. {min(pages)}-{max(pages)}" if len(pages) > 1 else f"str. {pages[0]}"
        
        print(f"\n{i}. Podobnost: {result['similarity']:.3f} | {page_info}")
        print(f"   Dotaz: '{result['query']}'")
        
        # Extract key information from text
        text = result['text']
        if len(text) > 500:
            # Try to find sentence breaks
            sentences = text.split('.')
            summary_text = '. '.join(sentences[:3]) + '.'
            if len(summary_text) > 500:
                summary_text = text[:500] + '...'
        else:
            summary_text = text
            
        print(f"   📄 {summary_text}")
    
    if medium_relevance:
        print(f"\n🔸 STŘEDNĚ RELEVANTNÍ USTANOVENÍ ({len(medium_relevance)} chunků):")
        print("-" * 60)
        
        for i, result in enumerate(medium_relevance[:5], 1):
            pages = result['pages']
            page_info = f"str. {min(pages)}-{max(pages)}" if len(pages) > 1 else f"str. {pages[0]}"
            print(f"{i}. Podobnost: {result['similarity']:.3f} | {page_info} | '{result['query']}'")
    
    # Generate structured summary
    print(f"\n📝 STRUKTUROVANÉ SHRNUTÍ:")
    print("-" * 60)
    
    # Analyze content for key topics
    all_text = ' '.join([r['text'] for r in high_relevance])
    
    topics = {
        'Důvody rozvodu': ['důvod', 'příčin', 'rozvrácen'],
        'Řízení': ['řízení', 'soud', 'návrh', 'žaloba'],
        'Děti': ['dítě', 'děti', 'péče', 'výchova'],
        'Majetek': ['majetek', 'jmění', 'podíl', 'vypořádání'],
        'Výživné': ['výživné', 'alimenty', 'vyživovací']
    }
    
    for topic, keywords in topics.items():
        if any(keyword in all_text.lower() for keyword in keywords):
            relevant_chunks = [r for r in high_relevance 
                             if any(keyword in r['text'].lower() for keyword in keywords)]
            if relevant_chunks:
                print(f"\n• {topic}: Nalezeno {len(relevant_chunks)} relevantních ustanovení")
                # Show page references
                pages = set()
                for chunk in relevant_chunks[:3]:
                    pages.update(chunk['pages'])
                if pages:
                    page_list = sorted(list(pages))
                    print(f"  Stránky: {', '.join(map(str, page_list[:10]))}")

def main():
    try:
        results = search_divorce_topics()
        generate_summary(results)
        
        print(f"\n💡 TIP: Pro detailní studium použijte interaktivní vyhledávání:")
        print("   python interactive_search.py")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")

if __name__ == "__main__":
    main()
