from sentence_transformers import SentenceTransformer
from supabase import create_client, Client
import sys

# Configuration
SUPABASE_URL = "https://emohcpmxwwqhlbzfqklm.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtb2hjcG14d3dxaGxiemZxa2xtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNTAxNSwiZXhwIjoyMDY2NjExMDE1fQ.Gz4oKMdcB5_90huXT4r6fJWSIGgnwi4shYIgG7KkPec"
EMBEDDING_MODEL = "all-MiniLM-L6-v2"

class SemanticSearchEngine:
    def __init__(self):
        print("🚀 Inicializuji sémantické vyhledávání...")
        self.supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        self.model = SentenceTransformer(EMBEDDING_MODEL)
        print("✅ Připraveno!")
    
    def search(self, query, limit=5, min_similarity=0.1):
        """Perform semantic search on document chunks."""
        print(f"\n🔍 Vyhledávám: '{query}'")
        
        # Generate embedding for the query
        query_embedding = self.model.encode(query).tolist()
        
        # Perform vector similarity search
        result = self.supabase.rpc(
            'match_documents',
            {
                'query_embedding': query_embedding,
                'match_threshold': 0.0,
                'match_count': limit * 2  # Get more results to filter by similarity
            }
        ).execute()
        
        # Filter by minimum similarity
        filtered_results = [r for r in result.data if r['similarity'] >= min_similarity]
        
        if not filtered_results:
            print("❌ Nenalezeny žádné relevantní výsledky.")
            return
        
        print(f"📊 Nalezeno {len(filtered_results)} relevantních výsledků:\n")
        
        for i, item in enumerate(filtered_results[:limit], 1):
            similarity = item['similarity']
            pages = item['page_numbers']
            page_info = f"str. {min(pages)}-{max(pages)}" if len(pages) > 1 else f"str. {pages[0]}" if pages else "N/A"
            
            # Color coding based on similarity
            if similarity >= 0.3:
                emoji = "🎯"
            elif similarity >= 0.2:
                emoji = "🔸"
            else:
                emoji = "🔹"
            
            print(f"{i}. {emoji} Podobnost: {similarity:.3f} | {page_info}")
            print(f"   📄 {item['chunk_text'][:400]}{'...' if len(item['chunk_text']) > 400 else ''}")
            print()
    
    def get_stats(self):
        """Get database statistics."""
        result = self.supabase.table("document_chunks").select("*", count="exact").execute()
        return {
            'total_chunks': result.count,
            'document_name': 'Občanský zákoník (89/2012 Sb.)'
        }

def main():
    try:
        engine = SemanticSearchEngine()
        stats = engine.get_stats()
        
        print(f"\n📚 Databáze obsahuje {stats['total_chunks']} chunků z dokumentu: {stats['document_name']}")
        print("\n" + "="*80)
        print("🔍 SÉMANTICKÉ VYHLEDÁVÁNÍ V OBČANSKÉM ZÁKONÍKU")
        print("="*80)
        print("💡 Zadejte dotaz v češtině (např. 'manželství', 'vlastnictví', 'smlouva')")
        print("💡 Pro ukončení zadejte 'quit', 'exit' nebo 'q'")
        print("💡 Pro statistiky zadejte 'stats'")
        print("-"*80)
        
        while True:
            try:
                query = input("\n> ").strip()
                
                if query.lower() in ['quit', 'exit', 'q', '']:
                    print("👋 Nashledanou!")
                    break
                
                if query.lower() == 'stats':
                    stats = engine.get_stats()
                    print(f"\n📊 Statistiky:")
                    print(f"   📚 Dokument: {stats['document_name']}")
                    print(f"   📄 Celkem chunků: {stats['total_chunks']}")
                    continue
                
                if len(query) < 2:
                    print("⚠️  Zadejte alespoň 2 znaky")
                    continue
                
                engine.search(query, limit=5, min_similarity=0.1)
                
            except KeyboardInterrupt:
                print("\n👋 Nashledanou!")
                break
            except Exception as e:
                print(f"❌ Chyba při vyhledávání: {e}")
    
    except Exception as e:
        print(f"❌ Chyba při inicializaci: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
