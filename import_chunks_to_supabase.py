import json
import os
from sentence_transformers import SentenceTransformer
from supabase import create_client, Client
import uuid
from typing import List, Dict, Any
import time

# Configuration
SUPABASE_URL = "https://emohcpmxwwqhlbzfqklm.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtb2hjcG14d3dxaGxiemZxa2xtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNTAxNSwiZXhwIjoyMDY2NjExMDE1fQ.Gz4oKMdcB5_90huXT4r6fJWSIGgnwi4shYIgG7KkPec"
CHUNKS_FILE = "Sb_2012_89_2025-06-01_IZ.chunks.json"
DOCUMENT_NAME = "Sb_2012_89_2025-06-01_IZ"
EMBEDDING_MODEL = "all-MiniLM-L6-v2"
BATCH_SIZE = 10

def load_chunks(file_path: str) -> List[Dict[str, Any]]:
    """Load chunks from JSON file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def generate_embeddings(texts: List[str], model: SentenceTransformer) -> List[List[float]]:
    """Generate embeddings for a list of texts."""
    embeddings = model.encode(texts)
    return [embedding.tolist() for embedding in embeddings]

def prepare_chunk_data(chunks: List[Dict[str, Any]], embeddings: List[List[float]], document_name: str) -> List[Dict[str, Any]]:
    """Prepare chunk data for insertion into Supabase."""
    prepared_data = []
    
    for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
        # Extract document type from filename
        doc_type = "zákon" if "Sb_" in document_name else "pdf"
        
        # Create metadata with additional information
        metadata = {
            "source_file": CHUNKS_FILE,
            "document_title": "Občanský zákoník",
            "document_number": "89/2012 Sb.",
            "effective_date": "2025-06-01",
            "chunk_length": len(chunk["text"]),
            "page_span": chunk["end_page"] - chunk["start_page"] + 1
        }
        
        prepared_chunk = {
            "document_name": document_name,
            "document_type": doc_type,
            "chunk_text": chunk["text"],
            "chunk_index": i,
            "page_numbers": chunk["page_numbers"],
            "start_page": chunk["start_page"],
            "end_page": chunk["end_page"],
            "embedding": embedding,
            "metadata": metadata
        }
        
        prepared_data.append(prepared_chunk)
    
    return prepared_data

def insert_chunks_to_supabase(supabase: Client, chunks_data: List[Dict[str, Any]], batch_size: int = BATCH_SIZE):
    """Insert chunks into Supabase in batches."""
    total_chunks = len(chunks_data)
    print(f"Inserting {total_chunks} chunks into Supabase...")
    
    for i in range(0, total_chunks, batch_size):
        batch = chunks_data[i:i + batch_size]
        
        try:
            result = supabase.table("document_chunks").insert(batch).execute()
            print(f"Inserted batch {i//batch_size + 1}/{(total_chunks + batch_size - 1)//batch_size} ({len(batch)} chunks)")
            
            # Small delay to avoid rate limiting
            time.sleep(0.1)
            
        except Exception as e:
            print(f"Error inserting batch {i//batch_size + 1}: {e}")
            raise

def main():
    # Check if Supabase key is set
    if not SUPABASE_KEY:
        print("Error: SUPABASE_SERVICE_ROLE_KEY not configured")
        return
    
    # Initialize Supabase client
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    # Load chunks
    print(f"Loading chunks from {CHUNKS_FILE}...")
    chunks = load_chunks(CHUNKS_FILE)
    print(f"Loaded {len(chunks)} chunks")
    
    # Initialize embedding model
    print(f"Loading embedding model {EMBEDDING_MODEL}...")
    model = SentenceTransformer(EMBEDDING_MODEL)
    
    # Generate embeddings
    print("Generating embeddings...")
    texts = [chunk["text"] for chunk in chunks]
    embeddings = generate_embeddings(texts, model)
    print(f"Generated {len(embeddings)} embeddings")
    
    # Prepare data for insertion
    print("Preparing data for insertion...")
    chunks_data = prepare_chunk_data(chunks, embeddings, DOCUMENT_NAME)
    
    # Insert into Supabase
    insert_chunks_to_supabase(supabase, chunks_data)
    
    print("✅ Successfully imported all chunks to Supabase!")
    print(f"Document: {DOCUMENT_NAME}")
    print(f"Total chunks: {len(chunks_data)}")

if __name__ == "__main__":
    main()
