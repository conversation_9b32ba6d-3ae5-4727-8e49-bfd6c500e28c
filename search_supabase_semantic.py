import os
from sentence_transformers import SentenceTransformer
from supabase import create_client, Client
from typing import List, Dict, Any

# Configuration
SUPABASE_URL = "https://emohcpmxwwqhlbzfqklm.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtb2hjcG14d3dxaGxiemZxa2xtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNTAxNSwiZXhwIjoyMDY2NjExMDE1fQ.Gz4oKMdcB5_90huXT4r6fJWSIGgnwi4shYIgG7KkPec"
EMBEDDING_MODEL = "all-MiniLM-L6-v2"

def semantic_search(supabase: Client, model: SentenceTransformer, query: str, limit: int = 5, similarity_threshold: float = 0.7) -> List[Dict[str, Any]]:
    """
    Perform semantic search on document chunks.
    
    Args:
        supabase: Supabase client
        model: SentenceTransformer model
        query: Search query
        limit: Maximum number of results to return
        similarity_threshold: Minimum similarity score (0-1)
    
    Returns:
        List of matching chunks with similarity scores
    """
    # Generate embedding for the query
    query_embedding = model.encode(query).tolist()
    
    # Perform vector similarity search
    result = supabase.rpc(
        'match_documents',
        {
            'query_embedding': query_embedding,
            'match_threshold': similarity_threshold,
            'match_count': limit
        }
    ).execute()
    
    return result.data

def create_search_function(supabase: Client):
    """Create the match_documents function in Supabase for vector similarity search."""
    function_sql = """
    CREATE OR REPLACE FUNCTION match_documents (
        query_embedding vector(384),
        match_threshold float,
        match_count int
    )
    RETURNS TABLE (
        id uuid,
        document_name varchar,
        chunk_text text,
        chunk_index int,
        page_numbers int[],
        start_page int,
        end_page int,
        metadata jsonb,
        similarity float
    )
    LANGUAGE sql STABLE
    AS $$
        SELECT
            document_chunks.id,
            document_chunks.document_name,
            document_chunks.chunk_text,
            document_chunks.chunk_index,
            document_chunks.page_numbers,
            document_chunks.start_page,
            document_chunks.end_page,
            document_chunks.metadata,
            1 - (document_chunks.embedding <=> query_embedding) as similarity
        FROM document_chunks
        WHERE 1 - (document_chunks.embedding <=> query_embedding) > match_threshold
        ORDER BY document_chunks.embedding <=> query_embedding
        LIMIT match_count;
    $$;
    """
    
    try:
        supabase.postgrest.schema("public").rpc("exec", {"sql": function_sql}).execute()
        print("✅ Search function created successfully")
    except Exception as e:
        print(f"Error creating search function: {e}")

def format_search_results(results: List[Dict[str, Any]]) -> str:
    """Format search results for display."""
    if not results:
        return "No results found."
    
    formatted = []
    for i, result in enumerate(results, 1):
        similarity = result.get('similarity', 0)
        pages = result.get('page_numbers', [])
        page_info = f"str. {min(pages)}-{max(pages)}" if len(pages) > 1 else f"str. {pages[0]}" if pages else "N/A"
        
        formatted.append(f"""
{i}. Podobnost: {similarity:.3f} | {page_info}
   Text: {result['chunk_text'][:200]}{'...' if len(result['chunk_text']) > 200 else ''}
   Dokument: {result['document_name']}
   Metadata: {result.get('metadata', {})}
""")
    
    return "\n".join(formatted)

def main():
    if not SUPABASE_KEY:
        print("Error: SUPABASE_SERVICE_ROLE_KEY not configured")
        return
    
    # Initialize clients
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    model = SentenceTransformer(EMBEDDING_MODEL)
    
    # Create search function (run once)
    print("Setting up search function...")
    create_search_function(supabase)
    
    # Interactive search
    print("\n🔍 Sémantické vyhledávání v Občanském zákoníku")
    print("Zadejte dotaz (nebo 'quit' pro ukončení):")
    
    while True:
        query = input("\n> ").strip()
        
        if query.lower() in ['quit', 'exit', 'q']:
            break
        
        if not query:
            continue
        
        try:
            print(f"\nVyhledávám: '{query}'...")
            results = semantic_search(supabase, model, query)
            print(format_search_results(results))
            
        except Exception as e:
            print(f"Chyba při vyhledávání: {e}")

if __name__ == "__main__":
    main()
