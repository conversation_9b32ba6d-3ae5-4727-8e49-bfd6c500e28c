from supabase import create_client, Client

# Configuration
SUPABASE_URL = "https://emohcpmxwwqhlbzfqklm.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtb2hjcG14d3dxaGxiemZxa2xtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAzNTAxNSwiZXhwIjoyMDY2NjExMDE1fQ.Gz4oKMdcB5_90huXT4r6fJWSIGgnwi4shYIgG7KkPec"

def test_simple():
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    # Test 1: Simple select
    print("Test 1: Základní SELECT")
    result = supabase.table("document_chunks").select("id, chunk_text").limit(2).execute()
    print(f"Nalezeno {len(result.data)} záznamů")
    
    # Test 2: Get one embedding to test with
    print("\nTest 2: Získání jednoho embeddingu")
    result = supabase.table("document_chunks").select("embedding").limit(1).execute()
    if result.data:
        test_embedding = result.data[0]['embedding']
        print(f"Embedding z<PERSON><PERSON><PERSON>, délka: {len(test_embedding)}")
        
        # Test 3: Try the search function with this embedding
        print("\nTest 3: Test search funkce")
        try:
            search_result = supabase.rpc(
                'match_documents',
                {
                    'query_embedding': test_embedding,
                    'match_threshold': 0.1,  # Very low threshold
                    'match_count': 3
                }
            ).execute()
            print(f"Search výsledek: {len(search_result.data)} záznamů")
            for item in search_result.data:
                print(f"  - Podobnost: {item['similarity']:.3f}, Text: {item['chunk_text'][:100]}...")
        except Exception as e:
            print(f"Chyba při search: {e}")

if __name__ == "__main__":
    test_simple()
